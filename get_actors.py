#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演员列表获取脚本
使用 javdbid.py 中的已有函数获取 https://javdb.com/users/collection_actors 页面的演员列表
提取格式为 https://javdb.com/actors/**** 的网址并去重
"""

import re
import time
import urllib.parse
from typing import Set, List
from bs4 import BeautifulSoup

# 导入 javdbid.py 中的函数
from javdbid import init_driver, get_page, close_driver, driver


def extract_actor_urls(soup: BeautifulSoup, base_url: str) -> Set[str]:
    """
    从页面中提取演员链接
    
    Args:
        soup: BeautifulSoup 对象
        base_url: 基础URL
        
    Returns:
        Set[str]: 去重后的演员URL集合
    """
    actor_urls = set()
    
    # 查找所有链接
    links = soup.find_all('a', href=True)
    
    for link in links:
        href = link['href']
        # 检查是否为演员页面链接格式
        if re.match(r'^/actors/[^/]+/?$', href) or re.match(r'^https://javdb\.com/actors/[^/]+/?$', href):
            # 转换为完整URL
            full_url = urllib.parse.urljoin(base_url, href)
            actor_urls.add(full_url)
    
    return actor_urls


def get_next_page_url_actors(soup: BeautifulSoup, current_url: str) -> str:
    """
    获取下一页的URL（针对演员收藏页面）
    
    Args:
        soup: BeautifulSoup 对象
        current_url: 当前页面URL
        
    Returns:
        str: 下一页URL，如果没有则返回None
    """
    # 查找分页链接
    next_link = soup.find('a', {'rel': 'next'})
    if not next_link:
        # 尝试其他可能的分页选择器
        next_link = soup.find('a', class_='pagination-next')
        if not next_link:
            # 查找包含"下一页"或">"文本的链接
            for link in soup.find_all('a', href=True):
                text = link.get_text(strip=True)
                if text in ['下一页', '>', 'Next', '»']:
                    next_link = link
                    break
    
    if next_link and next_link.get('href'):
        return urllib.parse.urljoin(current_url, next_link['href'])
    
    return None


def get_actors_from_collection(start_url: str = "https://javdb.com/users/collection_actors") -> List[str]:
    """
    从演员收藏页面获取所有演员链接
    
    Args:
        start_url: 起始URL
        
    Returns:
        List[str]: 演员URL列表（已去重）
    """
    print(f"开始获取演员列表: {start_url}")
    print("请确保已在浏览器中登录 javdb.com")
    
    all_actor_urls = set()
    current_url = start_url
    page_count = 1
    
    try:
        while current_url:
            print(f"正在处理第 {page_count} 页: {current_url}")
            
            # 获取页面内容
            soup = get_page(current_url)
            if soup is None:
                print(f"无法获取页面内容: {current_url}")
                break
            
            # 提取演员链接
            page_actor_urls = extract_actor_urls(soup, current_url)
            print(f"第 {page_count} 页找到 {len(page_actor_urls)} 个演员链接")
            
            # 添加到总集合中
            all_actor_urls.update(page_actor_urls)
            
            # 获取下一页URL
            next_url = get_next_page_url_actors(soup, current_url)
            if next_url and next_url != current_url:
                current_url = next_url
                page_count += 1
                # 添加延时避免请求过快
                time.sleep(1)
            else:
                print("没有找到下一页，处理完成")
                break
                
            # 安全检查：避免无限循环
            if page_count > 100:  # 设置最大页数限制
                print("已达到最大页数限制，停止处理")
                break
    
    except KeyboardInterrupt:
        print("用户中断操作")
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
    
    # 转换为排序后的列表
    actor_list = sorted(list(all_actor_urls))
    print(f"总共获取到 {len(actor_list)} 个不重复的演员链接")
    
    return actor_list


def save_actors_to_file(actor_urls: List[str], filename: str = "actors_list.txt"):
    """
    将演员URL列表保存到文件
    
    Args:
        actor_urls: 演员URL列表
        filename: 保存的文件名
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            for url in actor_urls:
                f.write(url + '\n')
        print(f"演员列表已保存到: {filename}")
    except Exception as e:
        print(f"保存文件失败: {e}")


def wait_for_user_login():
    """等待用户手动登录确认"""
    from javdbid import driver

    print("\n=== 登录确认 ===")
    print("程序将打开浏览器并导航到登录页面")
    print("请手动完成登录操作，然后回到此窗口确认")

    # 先访问主页
    print("正在打开 javdb.com 主页...")
    driver.get("https://javdb.com")
    time.sleep(3)

    # 检查是否已经登录
    current_url = driver.current_url
    page_source = driver.page_source

    # 简单检查是否包含登录相关元素
    if "登录" in page_source or "login" in page_source.lower():
        print("检测到未登录状态，请手动登录")
    else:
        print("可能已经登录，请确认登录状态")

    print("\n请在浏览器中完成以下操作：")
    print("1. 如果未登录，请点击登录按钮并完成登录")
    print("2. 登录成功后，请确认可以正常访问个人收藏页面")
    print("3. 完成后，回到此窗口按 Enter 键继续...")

    input("\n按 Enter 键确认已完成登录...")

    # 再次检查登录状态
    print("正在验证登录状态...")
    try:
        # 尝试访问收藏页面来验证登录状态
        driver.get("https://javdb.com/users/collection_actors")
        time.sleep(3)

        current_url = driver.current_url
        page_source = driver.page_source

        # 检查是否被重定向到登录页面
        if "login" in current_url.lower() or "登录" in page_source:
            print("⚠️  警告：似乎仍未登录，但程序将继续执行")
            print("如果后续获取失败，请重新运行程序并确保正确登录")
        else:
            print("✓ 登录状态验证通过")

    except Exception as e:
        print(f"验证登录状态时出错: {e}")
        print("程序将继续执行，如果获取失败请检查登录状态")


def main():
    """主函数"""
    print("=== JAVDB 演员列表获取工具 ===")
    print("本工具将获取 https://javdb.com/users/collection_actors 页面的所有演员链接")
    print("需要用户手动登录 javdb.com 后才能继续")

    try:
        # 初始化浏览器驱动
        print("\n正在初始化浏览器...")
        init_driver(headless=False)  # 不使用无头模式，方便用户登录

        # 等待用户手动登录
        wait_for_user_login()

        # 获取演员列表
        print("\n=== 开始获取演员列表 ===")
        actor_urls = get_actors_from_collection()
        
        if actor_urls:
            # 显示结果
            print("\n=== 获取结果 ===")
            for i, url in enumerate(actor_urls[:10], 1):  # 显示前10个作为示例
                print(f"{i}. {url}")
            
            if len(actor_urls) > 10:
                print(f"... 还有 {len(actor_urls) - 10} 个演员链接")
            
            # 保存到文件
            save_actors_to_file(actor_urls)
            
            print(f"\n处理完成！共获取 {len(actor_urls)} 个演员链接")
        else:
            print("未获取到任何演员链接，请检查：")
            print("1. 是否已正确登录 javdb.com")
            print("2. 网络连接是否正常")
            print("3. 页面结构是否发生变化")
    
    except Exception as e:
        print(f"程序执行出错: {e}")
    
    finally:
        # 关闭浏览器驱动
        print("正在关闭浏览器...")
        close_driver()


if __name__ == "__main__":
    main()
