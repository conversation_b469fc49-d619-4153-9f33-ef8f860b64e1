#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版演员列表获取脚本
使用临时profile，手动登录
"""

import re
import urllib.parse
import tempfile
import time
from typing import Set, List
import undetected_chromedriver as uc
from selenium.webdriver.chrome.options import Options as ChromeOptions
from bs4 import BeautifulSoup
import argparse


def extract_actor_urls(soup) -> Set[str]:
    """从页面中提取演员页面的URL"""
    actor_urls = set()
    
    if soup is None:
        return actor_urls
    
    # 查找所有链接
    links = soup.find_all('a', href=True)
    print(f"页面中找到 {len(links)} 个链接")
    
    for link in links:
        href = link.get('href', '')
        
        # 检查是否是演员页面链接
        if '/actors/' in href:
            # 构建完整URL
            if href.startswith('/'):
                full_url = 'https://javdb.com' + href
            elif href.startswith('http'):
                full_url = href
            else:
                continue
            
            # 验证URL格式是否正确，排除分类页面
            if re.match(r'https://javdb\.com/actors/[^/]+/?$', full_url):
                # 排除明显的分类链接
                if not any(category in full_url.lower() for category in ['censored', 'uncensored', 'western']):
                    # 移除末尾的斜杠以统一格式
                    clean_url = full_url.rstrip('/')
                    actor_urls.add(clean_url)
                    print(f"找到演员链接: {clean_url}")
    
    return actor_urls


def get_page_with_driver(driver, url):
    """使用driver获取页面内容"""
    try:
        driver.get(url)
        time.sleep(3)
        page_source = driver.page_source
        return BeautifulSoup(page_source, 'html.parser')
    except Exception as e:
        print(f"获取页面失败: {e}")
        return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="简化版JAVDB演员列表获取")
    parser.add_argument("--url", 
                       default="https://javdb.com/users/collection_actors",
                       help="要抓取的页面URL")
    parser.add_argument("--output", "-o",
                       default="actors_simple.txt",
                       help="输出文件名")
    
    args = parser.parse_args()
    
    # 创建Chrome选项
    options = ChromeOptions()
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-blink-features=AutomationControlled")
    
    # 创建临时用户数据目录
    temp_user_data = tempfile.mkdtemp(prefix="chrome_actors_")
    options.add_argument(f"--user-data-dir={temp_user_data}")
    print(f"临时用户数据目录: {temp_user_data}")
    
    driver = None
    try:
        print("正在启动Chrome浏览器...")
        driver = uc.Chrome(options=options)
        print("浏览器启动成功")
        
        # 打开登录页面
        print("正在打开JAVDB登录页面...")
        driver.get("https://javdb.com/login")
        
        print("\n" + "="*60)
        print("请在浏览器中完成登录操作:")
        print("1. 输入用户名和密码")
        print("2. 完成任何验证码或安全验证")
        print("3. 确保成功登录到JAVDB")
        print("4. 登录完成后，按回车键继续...")
        print("="*60)
        
        # 等待用户完成登录
        input("登录完成后请按回车键继续...")
        
        # 访问目标页面
        print(f"正在访问目标页面: {args.url}")
        soup = get_page_with_driver(driver, args.url)
        
        if soup is None:
            print("无法获取页面内容")
            return
        
        # 保存页面用于调试
        with open("debug_actors_page.html", 'w', encoding='utf-8') as f:
            f.write(soup.prettify())
        print("页面已保存到: debug_actors_page.html")
        
        # 检查页面标题
        title = soup.find('title')
        if title:
            print(f"页面标题: {title.get_text()}")
        
        # 检查是否需要登录（更宽松的检测）
        page_text = soup.get_text()
        if "此內容需要登入" in page_text:
            print("❌ 页面显示需要登录")
            user_choice = input("如果您确认已经登录并看到了收藏页面，请输入 'y' 继续: ").lower().strip()
            if user_choice != 'y':
                return
        else:
            print("✅ 页面加载成功，继续提取演员链接")
        
        # 提取演员URL
        print("正在提取演员链接...")
        actor_urls = extract_actor_urls(soup)

        # 如果没有找到演员链接，提供更多调试信息
        if not actor_urls:
            print("没有找到演员链接，正在分析页面内容...")

            # 查找所有包含 "actors" 的链接
            all_links = soup.find_all('a', href=True)
            actor_related_links = []
            for link in all_links:
                href = link.get('href', '')
                if 'actors' in href.lower():
                    actor_related_links.append(href)

            if actor_related_links:
                print(f"找到 {len(actor_related_links)} 个包含 'actors' 的链接:")
                for i, link in enumerate(actor_related_links[:10], 1):
                    print(f"  {i}. {link}")
            else:
                print("没有找到任何包含 'actors' 的链接")

            # 检查页面是否包含演员相关的中文内容
            if "演员" in page_text:
                print("页面包含'演员'关键词")
            if "收藏" in page_text:
                print("页面包含'收藏'关键词")

        if actor_urls:
            # 保存到文件
            sorted_urls = sorted(list(actor_urls))
            with open(args.output, 'w', encoding='utf-8') as f:
                for url in sorted_urls:
                    f.write(url + '\n')
            
            print(f"✅ 成功找到 {len(sorted_urls)} 个演员链接")
            print(f"结果已保存到: {args.output}")
            
            # 显示前10个URL
            print("\n前10个演员URL:")
            for i, url in enumerate(sorted_urls[:10], 1):
                print(f"{i:2d}. {url}")
            
            if len(sorted_urls) > 10:
                print(f"... 还有 {len(sorted_urls) - 10} 个URL")
        else:
            print("❌ 没有找到任何演员链接")
            print("可能的原因:")
            print("1. 登录未成功")
            print("2. 页面结构发生变化")
            print("3. URL不正确")
    
    except Exception as e:
        print(f"程序执行出错: {e}")
    
    finally:
        if driver:
            print("正在关闭浏览器...")
            driver.quit()


if __name__ == "__main__":
    main()
